# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a standard Flutter application named `eva_frontend` - a basic Flutter project created from the default template. It follows Flutter's standard architecture with minimal customization.

## Common Development Commands

### Building and Running
- `flutter run` - Run the app in debug mode on connected device/emulator
- `flutter build apk` - Build APK for Android
- `flutter build ios` - Build for iOS (requires macOS)
- `flutter build web` - Build for web deployment

### Development Tools
- `flutter analyze` - Run static analysis and linting (uses rules from analysis_options.yaml)
- `flutter test` - Run all unit and widget tests
- `flutter test test/widget_test.dart` - Run specific test file
- `flutter pub get` - Install dependencies
- `flutter clean` - Clean build cache and artifacts
- `flutter doctor` - Check Flutter installation and dependencies

### Package Management
- `flutter pub add <package_name>` - Add new dependency
- `flutter pub upgrade` - Upgrade all dependencies
- `flutter pub outdated` - Check for outdated dependencies

## Project Structure

- `lib/main.dart` - Main application entry point with default counter app
- `test/widget_test.dart` - Basic widget test for the counter functionality
- `pubspec.yaml` - Project configuration and dependencies
- `analysis_options.yaml` - Dart analyzer configuration using flutter_lints

## Development Dependencies

- Uses `flutter_lints: ^5.0.0` for code quality enforcement
- Standard Flutter SDK ^3.8.1
- Basic Material Design with `cupertino_icons` for iOS-style icons

## Current State

This is a fresh Flutter project with the default counter app implementation. The main application consists of:
- `MyApp` - Root MaterialApp widget
- `MyHomePage` - Stateful widget with counter functionality
- Basic Material Design theme with deep purple color scheme