import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildTopBar(),
            _buildTabBar(),
            Expanded(
              child: _buildChatArea(),
            ),
            _buildBottomInputArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.menu, color: Colors.black87),
            onPressed: () {},
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.edit_note, color: Colors.black87, size: 28),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildTab('Ask', 0, Icons.search),
          const SizedBox(width: 16),
          _buildTab('Imagine', 1, Icons.auto_awesome_outlined),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index, IconData icon) {
    return Obx(() {
      final isSelected = controller.selectedTabIndex.value == index;
      return GestureDetector(
        onTap: () => controller.selectTab(index),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? Colors.grey[100] : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? Colors.grey[300]! : Colors.transparent,
            ),
          ),
          child: Row(
            children: [
              Icon(icon, size: 20, color: Colors.black87),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildChatArea() {
    return Obx(() => ListView.builder(
      padding: const EdgeInsets.all(16),
      reverse: true,
      itemCount: controller.messages.length,
      itemBuilder: (context, index) {
        final reversedIndex = controller.messages.length - 1 - index;
        final message = controller.messages[reversedIndex];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildMessage(message),
            if (message.responseTime != null)
              Padding(
                padding: const EdgeInsets.only(top: 4, bottom: 16),
                child: _buildTimestamp(message.responseTime!),
              ),
            const SizedBox(height: 16),
          ],
        );
      },
    ));
  }

  Widget _buildMessage(Message message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: message.isUser ? Colors.grey[100] : Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          message.text,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  Widget _buildTimestamp(String time) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          time,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              _buildActionButton(Icons.message_outlined, () {}),
              _buildActionButton(Icons.upload_outlined, () {}),
              _buildActionButton(Icons.thumb_up_outlined, () => controller.likeMessage(0)),
              _buildActionButton(Icons.thumb_down_outlined, () => controller.dislikeMessage(0)),
              _buildActionButton(Icons.refresh, controller.refreshConversation),
              _buildActionButton(Icons.more_horiz, controller.showMoreOptions),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(25),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.messageController,
                    decoration: const InputDecoration(
                      hintText: 'Ask Anything',
                      hintStyle: TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 16),
                    ),
                    onSubmitted: (_) => controller.sendMessage(),
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.attach_file, color: Colors.grey),
                      onPressed: controller.attachFile,
                    ),
                    Container(
                      width: 1,
                      height: 24,
                      color: Colors.grey[300],
                    ),
                    TextButton.icon(
                      icon: const Icon(Icons.flash_on, size: 18),
                      label: const Text('Fast'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.black87,
                      ),
                      onPressed: controller.toggleFastMode,
                    ),
                    IconButton(
                      icon: const Icon(Icons.visibility_outlined, color: Colors.grey),
                      onPressed: controller.showVisibilityOptions,
                    ),
                    IconButton(
                      icon: const Icon(Icons.lightbulb_outline, color: Colors.grey),
                      onPressed: controller.showSuggestions,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Obx(() => ElevatedButton.icon(
            onPressed: controller.toggleRecording,
            icon: Icon(
              controller.isRecording.value ? Icons.stop : Icons.mic,
              color: Colors.white,
            ),
            label: Text(
              controller.isRecording.value ? 'Stop' : 'Speak',
              style: const TextStyle(fontSize: 18, color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, VoidCallback onPressed) {
    return IconButton(
      icon: Icon(icon, color: Colors.grey[600], size: 22),
      onPressed: onPressed,
    );
  }
}