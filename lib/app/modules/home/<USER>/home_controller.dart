import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? timeDisplay;

  ChatMessage({
    required this.text,
    required this.isUser,
    DateTime? timestamp,
    this.timeDisplay,
  }) : timestamp = timestamp ?? DateTime.now();
}

class HomeController extends GetxController {
  final messageController = TextEditingController();
  final messages = <ChatMessage>[].obs;
  final currentMode = 'ask'.obs;
  final selectedSpeed = 'Fast'.obs;

  @override
  void onInit() {
    super.onInit();
    // 添加效果图中的对话内容
    messages.addAll([
      ChatMessage(text: "你好", isUser: true),
      ChatMessage(
        text: "你好！有什么我可以帮你的吗？", 
        isUser: false,
        timeDisplay: "1:14s"
      ),
      ChatMessage(text: "你会做什么", isUser: true),
      ChatMessage(
        text: "我是 Grok 3，由 xAI 创建。我可以：\n\n• 回答几乎任何问题，尽可能提供准确且有用的信息\n\n• 分析 X 帖子、用户简介和链接（如果需要实时数据，我可以搜索网络）\n\n• 处理上传的内容，比如图片、PDF或文本\n\n• 提供翻译、解释复杂概念或协助解决问题\n\n• 生成文本，比如文章、信件或创意写作\n\n• 如果你想让我生成图表或编辑图片，可以告诉我！解释复杂概念    人工智能发展\n\n• 我还能用中文或其他语言流畅交流！",
        isUser: false
      ),
    ]);
  }

  void setMode(String mode) {
    currentMode.value = mode;
  }

  void sendMessage() {
    if (messageController.text.trim().isEmpty) return;
    
    messages.add(ChatMessage(
      text: messageController.text.trim(),
      isUser: true,
    ));
    
    messageController.clear();
  }

  void setSpeed(String speed) {
    selectedSpeed.value = speed;
  }

  @override
  void onClose() {
    messageController.dispose();
    super.onClose();
  }
}
