import 'package:get/get.dart';
import 'package:flutter/material.dart';

class HomeController extends GetxController {
  final TextEditingController messageController = TextEditingController();
  
  var selectedTabIndex = 0.obs;
  var messages = <Message>[].obs;
  var isRecording = false.obs;
  var isLoading = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    // Add initial messages matching the design
    messages.add(Message(
      text: '你好',
      isUser: true,
      timestamp: DateTime.now(),
    ));
    messages.add(Message(
      text: '嗨！好久不见呀（虽然可能是第一次聊，但感觉会很投缘～），今天过得怎么样？有没有遇到什么有趣的事儿，或者想吐槽的小烦恼？',
      isUser: false,
      timestamp: DateTime.now(),
      responseTime: '0.86s',
    ));
  }
  
  void selectTab(int index) {
    selectedTabIndex.value = index;
  }
  
  void sendMessage() {
    if (messageController.text.trim().isEmpty) return;
    
    final userMessage = Message(
      text: messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );
    
    messages.add(userMessage);
    messageController.clear();
    
    // Simulate AI response
    _simulateAIResponse();
  }
  
  void _simulateAIResponse() {
    isLoading.value = true;
    
    Future.delayed(const Duration(seconds: 1), () {
      final aiMessage = Message(
        text: '我正在处理您的请求...',
        isUser: false,
        timestamp: DateTime.now(),
        responseTime: '1.2s',
      );
      messages.add(aiMessage);
      isLoading.value = false;
    });
  }
  
  void startRecording() {
    isRecording.value = true;
    // TODO: Implement voice recording
  }
  
  void stopRecording() {
    isRecording.value = false;
    // TODO: Stop recording and process voice
  }
  
  void toggleRecording() {
    if (isRecording.value) {
      stopRecording();
    } else {
      startRecording();
    }
  }
  
  void likeMessage(int index) {
    // TODO: Implement like functionality
    Get.snackbar('提示', '已点赞');
  }
  
  void dislikeMessage(int index) {
    // TODO: Implement dislike functionality
    Get.snackbar('提示', '已反馈');
  }
  
  void refreshConversation() {
    messages.clear();
    Get.snackbar('提示', '对话已刷新');
  }
  
  void showMoreOptions() {
    // TODO: Show more options menu
    Get.bottomSheet(
      Container(
        height: 200,
        color: Colors.white,
        child: const Center(
          child: Text('更多选项'),
        ),
      ),
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }
  
  void attachFile() {
    // TODO: Implement file attachment
    Get.snackbar('提示', '文件附件功能开发中');
  }
  
  void toggleFastMode() {
    // TODO: Toggle fast mode
    Get.snackbar('提示', '快速模式已切换');
  }
  
  void showVisibilityOptions() {
    // TODO: Show visibility options
    Get.snackbar('提示', '可见性选项');
  }
  
  void showSuggestions() {
    // TODO: Show AI suggestions
    Get.snackbar('提示', 'AI建议功能开发中');
  }
  
  @override
  void onClose() {
    messageController.dispose();
    super.onClose();
  }
}

class Message {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? responseTime;
  
  Message({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.responseTime,
  });
}