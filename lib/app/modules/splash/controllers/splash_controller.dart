import 'package:get/get.dart';
import '../../../routes/app_pages.dart';

class SplashController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    print('[SPLASH] Controller initialized');
    _navigateToNext();
  }

  void _navigateToNext() {
    print('[SPLASH] Starting navigation timer');
    Future.delayed(const Duration(seconds: 3), () {
      try {
        print('[SPLASH] Attempting to navigate to login: ${Routes.login}');
        Get.offAllNamed(Routes.login);
        print('[SPLASH] Navigation command sent');
      } catch (e) {
        print('[SPLASH] Navigation error: $e');
        // Fallback navigation
        Get.offAllNamed('/login');
      }
    });
  }
}