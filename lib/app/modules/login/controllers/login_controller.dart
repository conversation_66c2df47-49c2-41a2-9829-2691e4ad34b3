import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import '../../../routes/app_pages.dart';

class LoginController extends GetxController {
  final TextEditingController emailPhoneController = TextEditingController();
  final TextEditingController verificationCodeController = TextEditingController();
  
  var isLoading = false.obs;
  var isCodeSent = false.obs;
  var userInput = ''.obs;
  
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  
  void onEmailPhoneChanged(String value) {
    userInput.value = value;
  }
  
  bool isEmail(String input) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(input);
  }
  
  bool isPhoneNumber(String input) {
    return RegExp(r'^\+?[\d\s-()]{10,}$').hasMatch(input);
  }
  
  void sendVerificationCode() {
    if (userInput.value.isEmpty) {
      Get.snackbar('Error', 'Please enter email or phone number');
      return;
    }
    
    if (!isEmail(userInput.value) && !isPhoneNumber(userInput.value)) {
      Get.snackbar('Error', 'Please enter a valid email or phone number');
      return;
    }
    
    isLoading.value = true;
    
    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      isLoading.value = false;
      isCodeSent.value = true;
      Get.snackbar('Success', 'Verification code sent!');
    });
  }
  
  void verifyCode() {
    if (verificationCodeController.text.isEmpty) {
      Get.snackbar('Error', 'Please enter verification code');
      return;
    }
    
    isLoading.value = true;
    
    // Simulate verification
    Future.delayed(const Duration(seconds: 1), () {
      try {
        isLoading.value = false;
        print('[LOGIN] Navigating to home: ${Routes.home}');
        Get.offAllNamed(Routes.home);
      } catch (e) {
        print('[LOGIN] Navigation error: $e');
        isLoading.value = false;
        Get.snackbar('Error', 'Navigation failed');
      }
    });
  }
  
  Future<void> signInWithGoogle() async {
    Get.offAllNamed(Routes.agent);
    return;
    try {
      isLoading.value = true;
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser != null) {
        print('[LOGIN] Google sign in successful, navigating to home');
        Get.offAllNamed(Routes.home);
      }
    } catch (error) {
      print('[LOGIN] Google sign in error: $error');
      Get.snackbar('Error', 'Google sign in failed');
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> signInWithFacebook() async {
    try {
      isLoading.value = true;
      final LoginResult result = await FacebookAuth.instance.login();
      
      if (result.status == LoginStatus.success) {
        print('[LOGIN] Facebook sign in successful, navigating to home');
        Get.offAllNamed(Routes.home);
      }
    } catch (error) {
      print('[LOGIN] Facebook sign in error: $error');
      Get.snackbar('Error', 'Facebook sign in failed');
    } finally {
      isLoading.value = false;
    }
  }
  
  @override
  void onClose() {
    emailPhoneController.dispose();
    verificationCodeController.dispose();
    super.onClose();
  }
}