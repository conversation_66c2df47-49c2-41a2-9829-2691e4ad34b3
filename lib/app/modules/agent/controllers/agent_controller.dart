import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AgentController extends GetxController {
  final TextEditingController messageController = TextEditingController();
  final ScrollController scrollController = ScrollController();
  
  // Chat state
  var messages = <ChatMessage>[].obs;
  var isTyping = false.obs;
  var isRecording = false.obs;
  var modelVersion = 'ChatGPT 4o'.obs;
  
  @override
  void onInit() {
    super.onInit();
    // Add welcome message
    messages.add(ChatMessage(
      text: '收到，测试成功 ✅ 有需要随时叫我！',
      isUser: false,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    ));
  }
  
  void sendMessage() {
    if (messageController.text.trim().isEmpty) return;
    
    // Add user message
    final userMessage = ChatMessage(
      text: messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    );
    
    messages.add(userMessage);
    messageController.clear();
    
    // Scroll to bottom
    _scrollToBottom();
    
    // Simulate AI response
    _generateResponse();
  }
  
  void _generateResponse() {
    isTyping.value = true;
    
    // Simulate typing delay
    Future.delayed(const Duration(seconds: 1, milliseconds: 500), () {
      final lastUserMessage = messages.lastWhere((msg) => msg.isUser).text.toLowerCase();
      
      String response;
      if (lastUserMessage.contains('测试')) {
        response = '收到，测试成功 ✅ 有需要随时叫我！';
      } else if (lastUserMessage.contains('你好') || lastUserMessage.contains('hi') || lastUserMessage.contains('hello')) {
        response = '你好！我是 Eva，你的 AI 助手。有什么我可以帮助你的吗？😊';
      } else if (lastUserMessage.contains('天气')) {
        response = '抱歉，我目前还不能查询实时天气信息。不过我可以帮你处理其他任务，比如回答问题、提供建议、帮助写作等。有什么其他需要帮助的吗？';
      } else if (lastUserMessage.contains('谢谢')) {
        response = '不客气！很高兴能帮到你。还有什么其他问题吗？';
      } else {
        final responses = [
          '这是一个很有意思的问题。让我来为你详细解答...\n\n根据我的理解，${_extractKeyTopic(lastUserMessage)}是一个值得深入探讨的话题。我可以从几个方面来分析这个问题。',
          '我理解你的需求。关于${_extractKeyTopic(lastUserMessage)}，我有一些想法和建议可以分享给你。',
          '好的，让我来帮你处理这个关于${_extractKeyTopic(lastUserMessage)}的问题。我会尽力提供有价值的信息和建议。',
          '收到你的消息了。针对${_extractKeyTopic(lastUserMessage)}这个话题，我来为你提供一些专业的见解和实用的建议。',
        ];
        
        response = responses[DateTime.now().millisecond % responses.length];
      }
      
      messages.add(ChatMessage(
        text: response,
        isUser: false,
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      ));
      
      isTyping.value = false;
      _scrollToBottom();
    });
  }
  
  String _extractKeyTopic(String message) {
    // Simple keyword extraction
    if (message.length > 20) {
      return '这个问题';
    }
    return message;
  }
  
  void _scrollToBottom() {
    if (scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }
  
  void toggleRecording() {
    isRecording.value = !isRecording.value;
    if (isRecording.value) {
      HapticFeedback.lightImpact();
      // TODO: Start recording
    } else {
      // TODO: Stop recording and process
    }
  }
  
  void copyMessage(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Get.snackbar(
      '已复制',
      '消息已复制到剪贴板',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      borderRadius: 8,
    );
  }
  
  void regenerateResponse() {
    if (messages.isNotEmpty && !messages.last.isUser) {
      messages.removeLast();
      _generateResponse();
    }
  }
  
  void likeMessage() {
    Get.snackbar(
      '反馈已记录',
      '感谢你的反馈！',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      borderRadius: 8,
    );
  }
  
  void dislikeMessage() {
    Get.snackbar(
      '反馈已记录',
      '我们会努力改进',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      borderRadius: 8,
    );
  }
  
  void shareMessage() {
    // TODO: Implement share functionality
    Get.snackbar(
      '分享',
      '分享功能即将推出',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: const EdgeInsets.all(8),
      borderRadius: 8,
    );
  }
  
  void attachFile() {
    // TODO: Implement file attachment
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.image),
              title: const Text('选择图片'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '图片选择功能开发中');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_present),
              title: const Text('选择文件'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '文件选择功能开发中');
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('拍照'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '相机功能开发中');
              },
            ),
          ],
        ),
      ),
    );
  }
  
  void showMenu() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('对话历史'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '对话历史功能开发中');
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('设置'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '设置功能开发中');
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('帮助'),
              onTap: () {
                Get.back();
                Get.snackbar('提示', '帮助功能开发中');
              },
            ),
          ],
        ),
      ),
    );
  }
  
  void startNewChat() {
    messages.clear();
    messages.add(ChatMessage(
      text: '你好！我是 Eva，你的 AI 助手。有什么可以帮助你的吗？',
      isUser: false,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
    ));
  }
  
  @override
  void onClose() {
    messageController.dispose();
    scrollController.dispose();
    super.onClose();
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final MessageStatus status;
  
  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    required this.status,
  });
}

enum MessageStatus {
  sending,
  sent,
  failed,
}