import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AgentController extends GetxController {
  final TextEditingController messageController = TextEditingController();
  
  // Agent info
  final agentName = '今日如何'.obs;
  final agentDescription = '内容由 AI 生成'.obs;
  
  // Chat state
  var messages = <AgentMessage>[].obs;
  var isRecording = false.obs;
  var isLoading = false.obs;
  var isMuted = false.obs;
  
  // Bottom toolbar options
  var selectedToolOption = 0.obs;
  final toolOptions = [
    ToolOption(icon: Icons.psychology, label: '深度思考'),
    ToolOption(icon: Icons.auto_awesome, label: 'AI 生图'),
    ToolOption(icon: Icons.edit_document, label: '豆包 P 图'),
    ToolOption(icon: Icons.photo_camera, label: '照片'),
  ];
  
  @override
  void onInit() {
    super.onInit();
    // Add initial greeting message
    messages.add(AgentMessage(
      text: '嗨！好久不见呀（虽然可能是第一次聊，但感觉会很投缘～），今天过得怎么样？有没有遇到什么有趣的事儿，或者想吐槽的小烦恼？',
      isUser: false,
      timestamp: DateTime.now(),
      avatar: '🤖',
    ));
  }
  
  void sendMessage() {
    if (messageController.text.trim().isEmpty) return;
    
    final userMessage = AgentMessage(
      text: messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );
    
    messages.add(userMessage);
    messageController.clear();
    
    // Simulate AI response
    _generateAIResponse();
  }
  
  void _generateAIResponse() {
    isLoading.value = true;
    
    Future.delayed(const Duration(seconds: 2), () {
      final responses = [
        '听起来很有意思呢！能详细说说吗？',
        '我理解你的感受，这种情况确实挺常见的。',
        '哇，这个想法很棒！你是怎么想到的？',
        '嗯嗯，然后呢？发生了什么？',
      ];
      
      final response = responses[DateTime.now().millisecond % responses.length];
      
      messages.add(AgentMessage(
        text: response,
        isUser: false,
        timestamp: DateTime.now(),
        avatar: '🤖',
      ));
      
      isLoading.value = false;
    });
  }
  
  void toggleRecording() {
    isRecording.value = !isRecording.value;
    if (isRecording.value) {
      HapticFeedback.lightImpact();
      // TODO: Start voice recording
    } else {
      // TODO: Stop recording and process
    }
  }
  
  void toggleMute() {
    isMuted.value = !isMuted.value;
    Get.snackbar(
      '提示',
      isMuted.value ? '已静音' : '已取消静音',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 1),
    );
  }
  
  void makeCall() {
    Get.snackbar('提示', '通话功能开发中', snackPosition: SnackPosition.BOTTOM);
  }
  
  void copyMessage(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Get.snackbar('提示', '已复制到剪贴板', snackPosition: SnackPosition.BOTTOM);
  }
  
  void playAudio() {
    Get.snackbar('提示', '音频播放功能开发中', snackPosition: SnackPosition.BOTTOM);
  }
  
  void bookmarkMessage() {
    Get.snackbar('提示', '已收藏', snackPosition: SnackPosition.BOTTOM);
  }
  
  void shareMessage() {
    Get.snackbar('提示', '分享功能开发中', snackPosition: SnackPosition.BOTTOM);
  }
  
  void refreshChat() {
    messages.clear();
    messages.add(AgentMessage(
      text: '让我们重新开始吧！有什么想聊的吗？',
      isUser: false,
      timestamp: DateTime.now(),
      avatar: '🤖',
    ));
  }
  
  void selectToolOption(int index) {
    selectedToolOption.value = index;
    Get.snackbar(
      '提示',
      '已选择：${toolOptions[index].label}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  void openCamera() {
    Get.snackbar('提示', '相机功能开发中', snackPosition: SnackPosition.BOTTOM);
  }
  
  void showMoreOptions() {
    Get.bottomSheet(
      Container(
        height: 300,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                '更多选项',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  void onClose() {
    messageController.dispose();
    super.onClose();
  }
}

class AgentMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? avatar;
  
  AgentMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.avatar,
  });
}

class ToolOption {
  final IconData icon;
  final String label;
  
  ToolOption({required this.icon, required this.label});
}