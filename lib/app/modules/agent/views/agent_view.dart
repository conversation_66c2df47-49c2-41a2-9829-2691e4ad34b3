import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/agent_controller.dart';

class AgentView extends GetView<AgentController> {
  const AgentView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildChatArea(),
            ),
            _buildActionBar(),
            _buildToolbar(),
            _buildInputArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => Get.back(),
          ),
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: Colors.purple[100],
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text(
                '💬',
                style: TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(() => Text(
                  controller.agentName.value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                )),
                Obx(() => Text(
                  controller.agentDescription.value,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                )),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.phone_outlined),
            onPressed: controller.makeCall,
          ),
          Obx(() => IconButton(
            icon: Icon(
              controller.isMuted.value ? Icons.volume_off : Icons.volume_up,
            ),
            onPressed: controller.toggleMute,
          )),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: controller.showMoreOptions,
          ),
        ],
      ),
    );
  }

  Widget _buildChatArea() {
    return Obx(() => ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.messages.length + (controller.isLoading.value ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == controller.messages.length && controller.isLoading.value) {
          return _buildTypingIndicator();
        }
        
        final message = controller.messages[index];
        return _buildMessage(message);
      },
    ));
  }

  Widget _buildMessage(AgentMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.purple[100],
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  message.avatar ?? '🤖',
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: message.isUser ? Colors.blue[50] : Colors.grey[100],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                message.text,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (message.isUser) const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.purple[100],
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Text('🤖', style: TextStyle(fontSize: 20)),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                _buildDot(0),
                const SizedBox(width: 4),
                _buildDot(1),
                const SizedBox(width: 4),
                _buildDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(int index) {
    return TweenAnimationBuilder(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 600 + (index * 200)),
      builder: (context, double value, child) {
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[400]!.withValues(alpha: value),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildActionBar() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildActionButton(Icons.copy_outlined, '复制', () {
            if (controller.messages.isNotEmpty) {
              controller.copyMessage(controller.messages.last.text);
            }
          }),
          _buildActionButton(Icons.volume_up_outlined, '音频', controller.playAudio),
          _buildActionButton(Icons.bookmark_outline, '收藏', controller.bookmarkMessage),
          _buildActionButton(Icons.share_outlined, '分享', controller.shareMessage),
          _buildActionButton(Icons.refresh, '刷新', controller.refreshChat),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24, color: Colors.blue[600]),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: controller.toolOptions.length,
        itemBuilder: (context, index) {
          final option = controller.toolOptions[index];
          return Obx(() => _buildToolOption(
            option,
            index,
            controller.selectedToolOption.value == index,
          ));
        },
      ),
    );
  }

  Widget _buildToolOption(ToolOption option, int index, bool isSelected) {
    return GestureDetector(
      onTap: () => controller.selectToolOption(index),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.grey[300]! : Colors.transparent,
          ),
        ),
        child: Row(
          children: [
            Icon(
              option.icon,
              size: 18,
              color: isSelected ? Colors.black : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Text(
              option.label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? Colors.black : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.camera_alt_outlined),
            onPressed: controller.openCamera,
          ),
          Expanded(
            child: Obx(() => GestureDetector(
              onTap: controller.toggleRecording,
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: controller.isRecording.value
                      ? Colors.purple[50]
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: controller.isRecording.value
                        ? Colors.purple[200]!
                        : Colors.transparent,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      controller.isRecording.value ? Icons.mic : Icons.mic_none,
                      color: controller.isRecording.value
                          ? Colors.purple[600]
                          : Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      controller.isRecording.value ? '正在录音...' : '按住说话',
                      style: TextStyle(
                        fontSize: 16,
                        color: controller.isRecording.value
                            ? Colors.purple[600]
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )),
          ),
          IconButton(
            icon: const Icon(Icons.keyboard_outlined),
            onPressed: () {
              // TODO: Switch to keyboard input
            },
          ),
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: controller.showMoreOptions,
          ),
        ],
      ),
    );
  }
}