import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/realtime_controller.dart';

class RealtimeView extends GetView<RealtimeController> {
  const RealtimeView({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildMainContent(),
            ),
            _buildControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: controller.switchToAgent,
          ),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Eva 实时对话',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Obx(() => Text(
                    controller.connectionStatus.value,
                    style: TextStyle(
                      fontSize: 12,
                      color: controller.isConnected.value 
                          ? Colors.green[400] 
                          : Colors.grey[400],
                    ),
                  )),
                ],
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildAudioVisualizer(),
                  const SizedBox(height: 40),
                  Obx(() => Text(
                    controller.isListening.value 
                        ? '正在聆听...' 
                        : controller.isSpeaking.value 
                            ? 'Eva 正在说话...'
                            : '点击开始对话',
                    style: const TextStyle(
                      fontSize: 20,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  )),
                  const SizedBox(height: 16),
                  Obx(() => controller.currentTranscript.value.isNotEmpty
                      ? Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            controller.currentTranscript.value,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : const SizedBox.shrink()),
                ],
              ),
            ),
          ),
          _buildConversationHistory(),
        ],
      ),
    );
  }

  Widget _buildAudioVisualizer() {
    return Obx(() => AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            controller.isListening.value
                ? Colors.blue.withOpacity(0.3)
                : controller.isSpeaking.value
                    ? Colors.purple.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
            Colors.transparent,
          ],
          stops: [
            controller.audioLevel.value * 0.8,
            1.0,
          ],
        ),
        border: Border.all(
          color: controller.isListening.value
              ? Colors.blue
              : controller.isSpeaking.value
                  ? Colors.purple
                  : Colors.grey,
          width: 3,
        ),
      ),
      child: Center(
        child: Icon(
          controller.isListening.value
              ? Icons.mic
              : controller.isSpeaking.value
                  ? Icons.volume_up
                  : Icons.mic_none,
          size: 80,
          color: Colors.white,
        ),
      ),
    ));
  }

  Widget _buildConversationHistory() {
    return Obx(() => controller.conversationText.value.isNotEmpty
        ? Container(
            height: 150,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: SingleChildScrollView(
              child: Text(
                controller.conversationText.value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  height: 1.5,
                ),
              ),
            ),
          )
        : const SizedBox.shrink());
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            Icons.chat_bubble_outline,
            '文字对话',
            controller.switchToAgent,
            Colors.grey,
          ),
          Obx(() => _buildMainControlButton()),
          _buildControlButton(
            Icons.call_end,
            '结束',
            controller.endCall,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildMainControlButton() {
    return GestureDetector(
      onTap: controller.toggleListening,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: controller.isListening.value
              ? Colors.red
              : Colors.blue,
          boxShadow: [
            BoxShadow(
              color: (controller.isListening.value
                      ? Colors.red
                      : Colors.blue)
                  .withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Center(
          child: Icon(
            controller.isListening.value ? Icons.stop : Icons.mic,
            size: 40,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    String label,
    VoidCallback onPressed,
    Color color,
  ) {
    return Column(
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: color, size: 28),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
          ),
        ),
      ],
    );
  }
}