import 'package:get/get.dart';
import 'package:flutter/material.dart';

class RealtimeController extends GetxController {
  // Realtime conversation state
  var isConnected = false.obs;
  var isListening = false.obs;
  var isSpeaking = false.obs;
  var connectionStatus = '未连接'.obs;
  var conversationText = ''.obs;
  var currentTranscript = ''.obs;
  
  // Audio level for visualization
  var audioLevel = 0.0.obs;
  
  @override
  void onInit() {
    super.onInit();
    // Initialize realtime connection
    _initializeConnection();
  }
  
  void _initializeConnection() {
    // Simulate connection setup
    connectionStatus.value = '正在连接...';
    Future.delayed(const Duration(seconds: 2), () {
      isConnected.value = true;
      connectionStatus.value = '已连接';
      Get.snackbar(
        '连接成功',
        '实时对话已准备就绪',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    });
  }
  
  void toggleListening() {
    if (!isConnected.value) {
      Get.snackbar(
        '未连接',
        '请等待连接建立',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return;
    }
    
    isListening.value = !isListening.value;
    
    if (isListening.value) {
      _startListening();
    } else {
      _stopListening();
    }
  }
  
  void _startListening() {
    // Simulate audio level changes
    _simulateAudioLevel();
    
    // Simulate transcript
    Future.delayed(const Duration(seconds: 2), () {
      if (isListening.value) {
        currentTranscript.value = '你好，请问有什么可以帮助你的吗？';
        _processTranscript();
      }
    });
  }
  
  void _stopListening() {
    audioLevel.value = 0.0;
    currentTranscript.value = '';
  }
  
  void _simulateAudioLevel() {
    if (!isListening.value) return;
    
    // Simulate audio level fluctuation
    audioLevel.value = (DateTime.now().millisecond % 100) / 100.0;
    
    Future.delayed(const Duration(milliseconds: 100), () {
      _simulateAudioLevel();
    });
  }
  
  void _processTranscript() {
    if (currentTranscript.value.isNotEmpty) {
      conversationText.value += '用户: ${currentTranscript.value}\n';
      
      // Simulate AI response
      isSpeaking.value = true;
      Future.delayed(const Duration(seconds: 1), () {
        final response = '好的，我理解了。让我来为你提供帮助。';
        conversationText.value += 'AI: $response\n\n';
        isSpeaking.value = false;
      });
    }
  }
  
  void endCall() {
    isListening.value = false;
    isSpeaking.value = false;
    audioLevel.value = 0.0;
    currentTranscript.value = '';
    
    Get.dialog(
      AlertDialog(
        title: const Text('结束对话'),
        content: const Text('确定要结束实时对话吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.back(); // Return to agent page
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  void switchToAgent() {
    Get.back();
  }
  
  @override
  void onClose() {
    isListening.value = false;
    isSpeaking.value = false;
    super.onClose();
  }
}