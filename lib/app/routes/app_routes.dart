part of 'app_pages.dart';

abstract class Routes {
  Routes._();
  static const splash = _Paths.splash;
  static const login = _Paths.login;
  static const home = _Paths.home;
  static const agent = _Paths.agent;
  static const realtime = _Paths.realtime;
}

abstract class _Paths {
  _Paths._();
  static const splash = '/splash';
  static const login = '/login';
  static const home = '/home';
  static const agent = '/agent';
  static const realtime = '/realtime';
}